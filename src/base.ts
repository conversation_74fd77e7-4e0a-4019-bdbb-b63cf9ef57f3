/**
 * @fileoverview Base types and interfaces for Google Apps Script
 */

import type { <PERSON><PERSON><PERSON>ber, <PERSON>te, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, JdbcSQL_XML } from "./types.ts";

/**
 * Console interface for logging
 */
export interface Console {
    error(): void;
    error(formatOrObject: object | string, ...values: any[]): void;
    info(): void;
    info(formatOrObject: object | string, ...values: any[]): void;
    log(): void;
    log(formatOrObject: object | string, ...values: any[]): void;
    time(label: string): void;
    timeEnd(label: string): void;
    warn(): void;
    warn(formatOrObject: object | string, ...values: any[]): void;
}

/**
 * A data interchange object for Apps Script services.
 */
export interface Blob extends BlobSource {
    copyBlob(): Blob;
    getAs(contentType: string): Blob;
    getBytes(): Byte[];
    getContentType(): string | null;
    getDataAsString(): string;
    getDataAsString(charset: string): string;
    getName(): string | null;
    isGoogleType(): boolean;
    setBytes(data: Byte[]): Blob;
    setContentType(contentType: string | null): Blob;
    setContentTypeFromExtension(): Blob;
    setDataFromString(string: string): Blob;
    setDataFromString(string: string, charset: string): Blob;
    setName(name: string): Blob;
    /** @deprecated DO NOT USE */ getAllBlobs(): Blob[];
}

/**
 * Interface for objects that can export their data as a Blob.
 */
export interface BlobSource {
    getAs(contentType: string): Blob;
    getBlob(): Blob;
}

/**
 * This class provides access to dialog boxes specific to Google Sheets.
 */
export interface Browser {
    Buttons: typeof ButtonSet;
    inputBox(prompt: string): string;
    inputBox(prompt: string, buttons: ButtonSet): string;
    inputBox(title: string, prompt: string, buttons: ButtonSet): string;
    msgBox(prompt: string): string;
    msgBox(prompt: string, buttons: ButtonSet): string;
    msgBox(title: string, prompt: string, buttons: ButtonSet): string;
}

/**
 * An enum representing predetermined, localized dialog buttons.
 */
export enum Button {
    CLOSE,
    OK,
    CANCEL,
    YES,
    NO,
}

/**
 * An enum representing predetermined, localized sets of dialog buttons.
 */
export enum ButtonSet {
    OK,
    OK_CANCEL,
    YES_NO,
    YES_NO_CANCEL,
}

/**
 * The types of Colors
 */
export enum ColorType {
    UNSUPPORTED,
    RGB,
    THEME,
}

/**
 * This class allows the developer to write out text to the debugging logs.
 */
export interface Logger {
    clear(): void;
    getLog(): string;
    log(data: any): Logger;
    log(format: string, ...values: any[]): Logger;
}

/**
 * A custom menu in an instance of the user interface for a Google App.
 */
export interface Menu {
    addItem(caption: string, functionName: string): Menu;
    addSeparator(): Menu;
    addSubMenu(menu: Menu): Menu;
    addToUi(): void;
}

/**
 * An enumeration that provides access to MIME-type declarations.
 */
export interface MimeType {
    GOOGLE_APPS_SCRIPT: string;
    GOOGLE_DRAWINGS: string;
    GOOGLE_DOCS: string;
    GOOGLE_FORMS: string;
    GOOGLE_SHEETS: string;
    GOOGLE_SITES: string;
    GOOGLE_SLIDES: string;
    FOLDER: string;
    SHORTCUT: string;
    BMP: string;
    GIF: string;
    JPEG: string;
    PNG: string;
    SVG: string;
    PDF: string;
    CSS: string;
    CSV: string;
    HTML: string;
    JAVASCRIPT: string;
    PLAIN_TEXT: string;
    RTF: string;
    OPENDOCUMENT_GRAPHICS: string;
    OPENDOCUMENT_PRESENTATION: string;
    OPENDOCUMENT_SPREADSHEET: string;
    OPENDOCUMENT_TEXT: string;
    MICROSOFT_EXCEL: string;
    MICROSOFT_EXCEL_LEGACY: string;
    MICROSOFT_POWERPOINT: string;
    MICROSOFT_POWERPOINT_LEGACY: string;
    MICROSOFT_WORD: string;
    MICROSOFT_WORD_LEGACY: string;
    ZIP: string;
}

/**
 * An enum representing the months of the year.
 */
export enum Month {
    JANUARY,
    FEBRUARY,
    MARCH,
    APRIL,
    MAY,
    JUNE,
    JULY,
    AUGUST,
    SEPTEMBER,
    OCTOBER,
    NOVEMBER,
    DECEMBER,
}

/**
 * A response to a prompt dialog.
 */
export interface PromptResponse {
    getResponseText(): string;
    getSelectedButton(): Button;
}

/**
 * A color defined by red, green, blue color channels.
 */
export interface RgbColor {
    asHexString(): string;
    getBlue(): Integer;
    getColorType(): ColorType;
    getGreen(): Integer;
    getRed(): Integer;
}

/**
 * The Session class provides access to session information.
 */
export interface Session {
    getActiveUser(): User;
    getActiveUserLocale(): string;
    getEffectiveUser(): User;
    getScriptTimeZone(): string;
    getTemporaryActiveUserKey(): string;
    /** @deprecated DO NOT USE */ getTimeZone(): string;
    /** @deprecated DO NOT USE */ getUser(): User;
}

/**
 * Representation of a user, suitable for scripting.
 */
export interface User {
    getEmail(): string;
    /** @deprecated DO NOT USE */ getUserLoginId(): string;
}

/**
 * An enum representing the days of the week.
 */
export enum Weekday {
    SUNDAY,
    MONDAY,
    TUESDAY,
    WEDNESDAY,
    THURSDAY,
    FRIDAY,
    SATURDAY,
}

/**
 * HTML output interface for UI dialogs
 */
export interface HtmlOutput {
    // This will be properly defined in the HTML module
    toString(): string;
}

/**
 * An instance of the user-interface environment for a Google App.
 */
export interface Ui {
    Button: typeof Button;
    ButtonSet: typeof ButtonSet;
    alert(prompt: string): Button;
    alert(prompt: string, buttons: ButtonSet): Button;
    alert(title: string, prompt: string, buttons: ButtonSet): Button;
    createAddonMenu(): Menu;
    createMenu(caption: string): Menu;
    prompt(prompt: string): PromptResponse;
    prompt(prompt: string, buttons: ButtonSet): PromptResponse;
    prompt(title: string, prompt: string, buttons: ButtonSet): PromptResponse;
    showModalDialog(userInterface: HtmlOutput, title: string): void;
    showModelessDialog(userInterface: HtmlOutput, title: string): void;
    showSidebar(userInterface: HtmlOutput): void;
    /** @deprecated DO NOT USE */ showDialog(userInterface: HtmlOutput): void;
}

/**
 * Apps Script has a non-standard Date Class
 */
export interface Date {
    toString(): string;
    toDateString(): string;
    toTimeString(): string;
    toLocaleString(): string;
    toLocaleDateString(): string;
    toLocaleTimeString(): string;
    valueOf(): number;
    getTime(): number;
    getFullYear(): number;
    getUTCFullYear(): number;
    getMonth(): number;
    getUTCMonth(): number;
    getDate(): number;
    getUTCDate(): number;
    getDay(): number;
    getUTCDay(): number;
    getHours(): number;
    getUTCHours(): number;
    getMinutes(): number;
    getUTCMinutes(): number;
    getSeconds(): number;
    getUTCSeconds(): number;
    getMilliseconds(): number;
    getUTCMilliseconds(): number;
    getTimezoneOffset(): number;
    setTime(time: number): number;
    setMilliseconds(ms: number): number;
    setUTCMilliseconds(ms: number): number;
    setSeconds(sec: number, ms?: number): number;
    setUTCSeconds(sec: number, ms?: number): number;
    setMinutes(min: number, sec?: number, ms?: number): number;
    setUTCMinutes(min: number, sec?: number, ms?: number): number;
    setHours(hours: number, min?: number, sec?: number, ms?: number): number;
    setUTCHours(hours: number, min?: number, sec?: number, ms?: number): number;
    setDate(date: number): number;
    setUTCDate(date: number): number;
    setMonth(month: number, date?: number): number;
    setUTCMonth(month: number, date?: number): number;
    setFullYear(year: number, month?: number, date?: number): number;
    setUTCFullYear(year: number, month?: number, date?: number): number;
    toUTCString(): string;
    toISOString(): string;
    toJSON(key?: unknown): string;
}

export interface DateConstructor {
    new(): Date;
    new(value: number | string): Date;
    new(
        year: number,
        month: number,
        date?: number,
        hours?: number,
        minutes?: number,
        seconds?: number,
        ms?: number,
    ): Date;
    (): string;
    readonly prototype: Date;
    parse(s: string): number;
    UTC(
        year: number,
        month: number,
        date?: number,
        hours?: number,
        minutes?: number,
        seconds?: number,
        ms?: number,
    ): number;
    now(): number;
}

// Global instances that would be available in Apps Script
export declare const Browser: Browser;
export declare const Logger: Logger;
export declare const MimeType: MimeType;
export declare const Session: Session;
export declare const console: Console;
export declare const Date: DateConstructor;
