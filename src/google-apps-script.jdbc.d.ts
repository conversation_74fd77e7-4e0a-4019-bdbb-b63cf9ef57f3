/// <reference path="google-apps-script.types.d.ts" />
/// <reference path="google-apps-script.base.d.ts" />

declare namespace GoogleAppsScript {
    namespace JDBC {
        /** JdbcAdvancedParameters */
        interface CloudSqlAdvancedParameters {
            /** connection timeout in seconds */
            connectTimeoutSeconds?: Integer | undefined;
            /** the database to connect to */
            database?: string | undefined;
            /** the name of a Google SQL Service instance */
            instance?: string | undefined;
            /** the user's password */
            password?: string | undefined;
            /** query timeout in seconds */
            queryTimeoutSeconds?: Integer | undefined;
            /** the username to pass to the database */
            user?: string | undefined;
        }
        /** JdbcAdvancedParameters */
        interface ConnectionAdvancedParameters {
            /** the database to connect to */
            databaseName?: string | undefined;
            /** the user's password */
            password?: string | undefined;
            /** whether or not the connection should comply with JDBC rules when converting time zones. The default is false. */
            useJDBCCompliantTimeZoneShift?: boolean | undefined;
            /** the username to pass to the database */
            user?: string | undefined;
            /** the server's SSL certificate */
            _serverSslCertificate?: string | undefined;
            /** the client's SSL certificate */
            _clientSslCertificate?: string | undefined;
            /** the client's SSL key */
            _clientSslKey?: string | undefined;
        }
        /**
         * The JDBC service allows scripts to connect to Google Cloud SQL, MySQL,
         * Microsoft SQL Server, and Oracle databases. For more information, see the guide to JDBC.
         */
        interface Jdbc {
            getCloudSqlConnection(url: string): JdbcConnection;
            getCloudSqlConnection(url: string, info: CloudSqlAdvancedParameters): JdbcConnection;
            getCloudSqlConnection(url: string, userName: string, password: string): JdbcConnection;
            getConnection(url: string): JdbcConnection;
            getConnection(url: string, info: ConnectionAdvancedParameters): JdbcConnection;
            getConnection(url: string, userName: string, password: string): JdbcConnection;
            newDate(milliseconds: Integer): JdbcDate;
            newTime(milliseconds: Integer): JdbcTime;
            newTimestamp(milliseconds: Integer): JdbcTimestamp;
            parseDate(date: string): JdbcDate;
            parseTime(time: string): JdbcTime;
            parseTimestamp(timestamp: string): JdbcTimestamp;
        }
        /**
         * A JDBC Array. For documentation of this class, see java.sql.Array
         * .
         */
        interface JdbcArray {
            free(): void;
            getArray(): any;
            getArray(index: Integer, count: Integer): any;
            getBaseType(): Integer;
            getBaseTypeName(): string;
            getResultSet(): JdbcResultSet;
            getResultSet(index: Integer, count: Integer): JdbcResultSet;
        }
        /**
         * A JDBC Blob. For documentation of this class, see java.sql.Blob
         * .
         */
        interface JdbcBlob {
            free(): void;
            getAppsScriptBlob(): Base.Blob;
            getAs(contentType: string): Base.Blob;
            getBytes(position: Integer, length: Integer): Byte[];
            length(): Integer;
            position(pattern: Byte[], start: Integer): Integer;
            position(pattern: JdbcBlob, start: Integer): Integer;
            setBytes(position: Integer, blobSource: Base.BlobSource): Integer;
            setBytes(position: Integer, blobSource: Base.BlobSource, offset: Integer, length: Integer): Integer;
            setBytes(position: Integer, bytes: Byte[]): Integer;
            setBytes(position: Integer, bytes: Byte[], offset: Integer, length: Integer): Integer;
            truncate(length: Integer): void;
        }
        /**
         * A JDBC CallableStatement. For documentation of this class, see
         * java.sql.CallableStatement.
         */
        interface JdbcCallableStatement {
            addBatch(): void;
            addBatch(sql: string): void;
            cancel(): void;
            clearBatch(): void;
            clearParameters(): void;
            clearWarnings(): void;
            close(): void;
            execute(): boolean;
            execute(sql: string): boolean;
            execute(sql: string, autoGeneratedKeys: Integer, sql_: string, columnIndexes: Integer[]): boolean;
            execute(sql: string, autoGeneratedKeys: Integer, sql_: string, columnIndexes: Integer[]): boolean;
            execute(sql: string, columnNames: string[]): boolean;
            executeBatch(): Integer[];
            executeQuery(): JdbcResultSet;
            executeQuery(sql: string): JdbcResultSet;
            executeUpdate(): Integer;
            executeUpdate(sql: string): Integer;
            executeUpdate(sql: string, autoGeneratedKeys: Integer, sql_: string, columnIndexes: Integer[]): Integer;
            executeUpdate(sql: string, autoGeneratedKeys: Integer, sql_: string, columnIndexes: Integer[]): Integer;
            executeUpdate(sql: string, columnNames: string[]): Integer;
            getArray(parameterIndex: Integer): JdbcArray;
            getArray(parameterName: string): JdbcArray;
            getBigDecimal(parameterIndex: Integer): BigNumber;
            getBigDecimal(parameterName: string): BigNumber;
            getBlob(parameterIndex: Integer): JdbcBlob;
            getBlob(parameterName: string): JdbcBlob;
            getBoolean(parameterIndex: Integer): boolean;
            getBoolean(parameterName: string): boolean;
            getByte(parameterIndex: Integer): Byte;
            getByte(parameterName: string): Byte;
            getBytes(parameterIndex: Integer): Byte[];
            getBytes(parameterName: string): Byte[];
            getClob(parameterIndex: Integer): JdbcClob;
            getClob(parameterName: string): JdbcClob;
            getConnection(): JdbcConnection;
            getDate(parameterIndex: Integer): JdbcDate;
            getDate(parameterIndex: Integer, timeZone: string): JdbcDate;
            getDate(parameterName: string): JdbcDate;
            getDate(parameterName: string, timeZone: string): JdbcDate;
            getDouble(parameterIndex: Integer): number;
            getDouble(parameterName: string): number;
            getFetchDirection(): Integer;
            getFetchSize(): Integer;
            getFloat(parameterIndex: Integer): number;
            getFloat(parameterName: string): number;
            getGeneratedKeys(): JdbcResultSet;
            getInt(parameterIndex: Integer): Integer;
            getInt(parameterName: string): Integer;
            getLong(parameterIndex: Integer): Integer;
            getLong(parameterName: string): Integer;
            getMaxFieldSize(): Integer;
            getMaxRows(): Integer;
            getMetaData(): JdbcResultSetMetaData;
            getMoreResults(): boolean;
            getMoreResults(current: Integer): boolean;
            getNClob(parameterIndex: Integer): JdbcClob;
            getNClob(parameterName: string): JdbcClob;
            getNString(parameterIndex: Integer): string;
            getNString(parameterName: string): string;
            getObject(parameterIndex: Integer): any;
            getObject(parameterName: string): any;
            getParameterMetaData(): JdbcParameterMetaData;
            getQueryTimeout(): Integer;
            getRef(parameterIndex: Integer): JdbcRef;
            getRef(parameterName: string): JdbcRef;
            getResultSet(): JdbcResultSet;
            getResultSetConcurrency(): Integer;
            getResultSetHoldability(): Integer;
            getResultSetType(): Integer;
            getRowId(parameterIndex: Integer): JdbcRowId;
            getRowId(parameterName: string): JdbcRowId;
            getSQLXML(parameterIndex: Integer): JdbcSQLXML;
            getSQLXML(parameterName: string): JdbcSQLXML;
            getShort(parameterIndex: Integer): Integer;
            getShort(parameterName: string): Integer;
            getString(parameterIndex: Integer): string;
            getString(parameterName: string): string;
            getTime(parameterIndex: Integer): JdbcTime;
            getTime(parameterIndex: Integer, timeZone: string): JdbcTime;
            getTime(parameterName: string): JdbcTime;
            getTime(parameterName: string, timeZone: string): JdbcTime;
            getTimestamp(parameterIndex: Integer): JdbcTimestamp;
            getTimestamp(parameterIndex: Integer, timeZone: string): JdbcTimestamp;
            getTimestamp(parameterName: string): JdbcTimestamp;
            getTimestamp(parameterName: string, timeZone: string): JdbcTimestamp;
            getURL(parameterIndex: Integer): string;
            getURL(parameterName: string): string;
            getUpdateCount(): Integer;
            getWarnings(): string[];
            isClosed(): boolean;
            isPoolable(): boolean;
            registerOutParameter(parameterIndex: Integer, sqlType: Integer): void;
            registerOutParameter(parameterIndex: Integer, sqlType: Integer, scale: Integer): void;
            registerOutParameter(parameterIndex: Integer, sqlType: Integer, typeName: string): void;
            registerOutParameter(parameterName: string, sqlType: Integer): void;
            registerOutParameter(parameterName: string, sqlType: Integer, scale: Integer): void;
            registerOutParameter(parameterName: string, sqlType: Integer, typeName: string): void;
            setArray(parameterIndex: Integer, x: JdbcArray): void;
            setBigDecimal(parameterIndex: Integer, x: BigNumber): void;
            setBigDecimal(parameterName: string, x: BigNumber): void;
            setBlob(parameterIndex: Integer, x: JdbcBlob): void;
            setBlob(parameterName: string, x: JdbcBlob): void;
            setBoolean(parameterIndex: Integer, x: boolean): void;
            setBoolean(parameterName: string, x: boolean): void;
            setByte(parameterIndex: Integer, x: Byte): void;
            setByte(parameterName: string, x: Byte): void;
            setBytes(parameterIndex: Integer, x: Byte[]): void;
            setBytes(parameterName: string, x: Byte[]): void;
            setClob(parameterIndex: Integer, x: JdbcClob): void;
            setClob(parameterName: string, x: JdbcClob): void;
            setCursorName(name: string): void;
            setDate(parameterIndex: Integer, x: JdbcDate): void;
            setDate(parameterIndex: Integer, x: JdbcDate, timeZone: string): void;
            setDate(parameterName: string, x: JdbcDate): void;
            setDate(parameterName: string, x: JdbcDate, timeZone: string): void;
            setDouble(parameterIndex: Integer, x: number): void;
            setDouble(parameterName: string, x: number): void;
            setEscapeProcessing(enable: boolean): void;
            setFetchDirection(direction: Integer): void;
            setFetchSize(rows: Integer): void;
            setFloat(parameterIndex: Integer, x: number): void;
            setFloat(parameterName: string, x: number): void;
            setInt(parameterIndex: Integer, x: Integer): void;
            setInt(parameterName: string, x: Integer): void;
            setLong(parameterIndex: Integer, x: Integer): void;
            setLong(parameterName: string, x: Integer): void;
            setMaxFieldSize(max: Integer): void;
            setMaxRows(max: Integer): void;
            setNClob(parameterIndex: Integer, x: JdbcClob): void;
            setNClob(parameterName: string, value: JdbcClob): void;
            setNString(parameterIndex: Integer, x: string): void;
            setNString(parameterName: string, value: string): void;
            setNull(parameterIndex: Integer, sqlType: Integer): void;
            setNull(parameterIndex: Integer, sqlType: Integer, typeName: string): void;
            setNull(parameterName: string, sqlType: Integer): void;
            setNull(parameterName: string, sqlType: Integer, typeName: string): void;
            setObject(index: Integer, x: any): void;
            setObject(parameterIndex: Integer, x: any, targetSqlType: Integer): void;
            setObject(parameterIndex: Integer, x: any, targetSqlType: Integer, scaleOrLength: Integer): void;
            setObject(parameterName: string, x: any): void;
            setObject(parameterName: string, x: any, targetSqlType: Integer): void;
            setObject(parameterName: string, x: any, targetSqlType: Integer, scale: Integer): void;
            setPoolable(poolable: boolean): void;
            setQueryTimeout(seconds: Integer): void;
            setRef(parameterIndex: Integer, x: JdbcRef): void;
            setRowId(parameterIndex: Integer, x: JdbcRowId): void;
            setRowId(parameterName: string, x: JdbcRowId): void;
            setSQLXML(parameterIndex: Integer, x: JdbcSQLXML): void;
            setSQLXML(parameterName: string, xmlObject: JdbcSQLXML): void;
            setShort(parameterIndex: Integer, x: Integer): void;
            setShort(parameterName: string, x: Integer): void;
            setString(parameterIndex: Integer, x: string): void;
            setString(parameterName: string, x: string): void;
            setTime(parameterIndex: Integer, x: JdbcTime): void;
            setTime(parameterIndex: Integer, x: JdbcTime, timeZone: string): void;
            setTime(parameterName: string, x: JdbcTime): void;
            setTime(parameterName: string, x: JdbcTime, timeZone: string): void;
            setTimestamp(parameterIndex: Integer, x: JdbcTimestamp): void;
            setTimestamp(parameterIndex: Integer, x: JdbcTimestamp, timeZone: string): void;
            setTimestamp(parameterName: string, x: JdbcTimestamp): void;
            setTimestamp(parameterName: string, x: JdbcTimestamp, timeZone: string): void;
            setURL(parameterIndex: Integer, x: string): void;
            setURL(parameterName: string, val: string): void;
            wasNull(): boolean;
        }
        /**
         * A JDBC Clob. For documentation of this class, see java.sql.Clob
         * .
         */
        interface JdbcClob {
            free(): void;
            getAppsScriptBlob(): Base.Blob;
            getAs(contentType: string): Base.Blob;
            getSubString(position: Integer, length: Integer): string;
            length(): Integer;
            position(search: JdbcClob, start: Integer): Integer;
            position(search: string, start: Integer): Integer;
            setString(position: Integer, blobSource: Base.BlobSource): Integer;
            setString(position: Integer, blobSource: Base.BlobSource, offset: Integer, len: Integer): Integer;
            setString(position: Integer, value: string): Integer;
            setString(position: Integer, value: string, offset: Integer, len: Integer): Integer;
            truncate(length: Integer): void;
        }
        /**
         * A JDBC Connection. For documentation of this class, see
         * java.sql.Connection.
         */
        interface JdbcConnection {
            clearWarnings(): void;
            close(): void;
            commit(): void;
            createArrayOf(typeName: string, elements: any[]): JdbcArray;
            createBlob(): JdbcBlob;
            createClob(): JdbcClob;
            createNClob(): JdbcClob;
            createSQLXML(): JdbcSQLXML;
            createStatement(): JdbcStatement;
            createStatement(resultSetType: Integer, resultSetConcurrency: Integer): JdbcStatement;
            createStatement(
                resultSetType: Integer,
                resultSetConcurrency: Integer,
                resultSetHoldability: Integer,
            ): JdbcStatement;
            createStruct(typeName: string, attributes: any[]): JdbcStruct;
            getAutoCommit(): boolean;
            getCatalog(): string;
            getHoldability(): Integer;
            getMetaData(): JdbcDatabaseMetaData;
            getTransactionIsolation(): Integer;
            getWarnings(): string[];
            isClosed(): boolean;
            isReadOnly(): boolean;
            isValid(timeout: Integer): boolean;
            nativeSQL(sql: string): string;
            prepareCall(sql: string): JdbcCallableStatement;
            prepareCall(sql: string, resultSetType: Integer, resultSetConcurrency: Integer): JdbcCallableStatement;
            prepareCall(
                sql: string,
                resultSetType: Integer,
                resultSetConcurrency: Integer,
                resultSetHoldability: Integer,
            ): JdbcCallableStatement;
            prepareStatement(sql: string): JdbcPreparedStatement;
            prepareStatement(sql: string, autoGeneratedKeys: Integer): JdbcPreparedStatement;
            prepareStatement(sql: string, resultSetType: Integer, resultSetConcurrency: Integer): JdbcPreparedStatement;
            prepareStatement(
                sql: string,
                resultSetType: Integer,
                resultSetConcurrency: Integer,
                resultSetHoldability: Integer,
            ): JdbcPreparedStatement;
            prepareStatementByIndex(sql: string, indices: Integer[]): JdbcPreparedStatement;
            prepareStatementByName(sql: string, columnNames: string[]): JdbcPreparedStatement;
            releaseSavepoint(savepoint: JdbcSavepoint): void;
            rollback(): void;
            rollback(savepoint: JdbcSavepoint): void;
            setAutoCommit(autoCommit: boolean): void;
            setCatalog(catalog: string): void;
            setHoldability(holdability: Integer): void;
            setReadOnly(readOnly: boolean): void;
            setSavepoint(): JdbcSavepoint;
            setSavepoint(name: string): JdbcSavepoint;
            setTransactionIsolation(level: Integer): void;
        }
        /**
         * A JDBC database metadata object. For documentation of this class, see
         * java.sql.DatabaseMetaData.
         */
        interface JdbcDatabaseMetaData {
            allProceduresAreCallable(): boolean;
            allTablesAreSelectable(): boolean;
            autoCommitFailureClosesAllResultSets(): boolean;
            dataDefinitionCausesTransactionCommit(): boolean;
            dataDefinitionIgnoredInTransactions(): boolean;
            deletesAreDetected(type: Integer): boolean;
            doesMaxRowSizeIncludeBlobs(): boolean;
            getAttributes(
                catalog: string,
                schemaPattern: string,
                typeNamePattern: string,
                attributeNamePattern: string,
            ): JdbcResultSet;
            getBestRowIdentifier(
                catalog: string,
                schema: string,
                table: string,
                scope: Integer,
                nullable: boolean,
            ): JdbcResultSet;
            getCatalogSeparator(): string;
            getCatalogTerm(): string;
            getCatalogs(): JdbcResultSet;
            getClientInfoProperties(): JdbcResultSet;
            getColumnPrivileges(
                catalog: string,
                schema: string,
                table: string,
                columnNamePattern: string,
            ): JdbcResultSet;
            getColumns(
                catalog: string,
                schemaPattern: string,
                tableNamePattern: string,
                columnNamePattern: string,
            ): JdbcResultSet;
            getConnection(): JdbcConnection;
            getCrossReference(
                parentCatalog: string,
                parentSchema: string,
                parentTable: string,
                foreignCatalog: string,
                foreignSchema: string,
                foreignTable: string,
            ): JdbcResultSet;
            getDatabaseMajorVersion(): Integer;
            getDatabaseMinorVersion(): Integer;
            getDatabaseProductName(): string;
            getDatabaseProductVersion(): string;
            getDefaultTransactionIsolation(): Integer;
            getDriverMajorVersion(): Integer;
            getDriverMinorVersion(): Integer;
            getDriverName(): string;
            getDriverVersion(): string;
            getExportedKeys(catalog: string, schema: string, table: string): JdbcResultSet;
            getExtraNameCharacters(): string;
            getFunctionColumns(
                catalog: string,
                schemaPattern: string,
                functionNamePattern: string,
                columnNamePattern: string,
            ): JdbcResultSet;
            getFunctions(catalog: string, schemaPattern: string, functionNamePattern: string): JdbcResultSet;
            getIdentifierQuoteString(): string;
            getImportedKeys(catalog: string, schema: string, table: string): JdbcResultSet;
            getIndexInfo(
                catalog: string,
                schema: string,
                table: string,
                unique: boolean,
                approximate: boolean,
            ): JdbcResultSet;
            getJDBCMajorVersion(): Integer;
            getJDBCMinorVersion(): Integer;
            getMaxBinaryLiteralLength(): Integer;
            getMaxCatalogNameLength(): Integer;
            getMaxCharLiteralLength(): Integer;
            getMaxColumnNameLength(): Integer;
            getMaxColumnsInGroupBy(): Integer;
            getMaxColumnsInIndex(): Integer;
            getMaxColumnsInOrderBy(): Integer;
            getMaxColumnsInSelect(): Integer;
            getMaxColumnsInTable(): Integer;
            getMaxConnections(): Integer;
            getMaxCursorNameLength(): Integer;
            getMaxIndexLength(): Integer;
            getMaxProcedureNameLength(): Integer;
            getMaxRowSize(): Integer;
            getMaxSchemaNameLength(): Integer;
            getMaxStatementLength(): Integer;
            getMaxStatements(): Integer;
            getMaxTableNameLength(): Integer;
            getMaxTablesInSelect(): Integer;
            getMaxUserNameLength(): Integer;
            getNumericFunctions(): string;
            getPrimaryKeys(catalog: string, schema: string, table: string): JdbcResultSet;
            getProcedureColumns(
                catalog: string,
                schemaPattern: string,
                procedureNamePattern: string,
                columnNamePattern: string,
            ): JdbcResultSet;
            getProcedureTerm(): string;
            getProcedures(catalog: string, schemaPattern: string, procedureNamePattern: string): JdbcResultSet;
            getResultSetHoldability(): Integer;
            getRowIdLifetime(): Integer;
            getSQLKeywords(): string;
            getSQLStateType(): Integer;
            getSchemaTerm(): string;
            getSchemas(): JdbcResultSet;
            getSchemas(catalog: string, schemaPattern: string): JdbcResultSet;
            getSearchStringEscape(): string;
            getStringFunctions(): string;
            getSuperTables(catalog: string, schemaPattern: string, tableNamePattern: string): JdbcResultSet;
            getSuperTypes(catalog: string, schemaPattern: string, typeNamePattern: string): JdbcResultSet;
            getSystemFunctions(): string;
            getTablePrivileges(catalog: string, schemaPattern: string, tableNamePattern: string): JdbcResultSet;
            getTableTypes(): JdbcResultSet;
            getTables(catalog: string, schemaPattern: string, tableNamePattern: string, types: string[]): JdbcResultSet;
            getTimeDateFunctions(): string;
            getTypeInfo(): JdbcResultSet;
            getUDTs(catalog: string, schemaPattern: string, typeNamePattern: string, types: Integer[]): JdbcResultSet;
            getURL(): string;
            getUserName(): string;
            getVersionColumns(catalog: string, schema: string, table: string): JdbcResultSet;
            insertsAreDetected(type: Integer): boolean;
            isCatalogAtStart(): boolean;
            isReadOnly(): boolean;
            locatorsUpdateCopy(): boolean;
            nullPlusNonNullIsNull(): boolean;
            nullsAreSortedAtEnd(): boolean;
            nullsAreSortedAtStart(): boolean;
            nullsAreSortedHigh(): boolean;
            nullsAreSortedLow(): boolean;
            othersDeletesAreVisible(type: Integer): boolean;
            othersInsertsAreVisible(type: Integer): boolean;
            othersUpdatesAreVisible(type: Integer): boolean;
            ownDeletesAreVisible(type: Integer): boolean;
            ownInsertsAreVisible(type: Integer): boolean;
            ownUpdatesAreVisible(type: Integer): boolean;
            storesLowerCaseIdentifiers(): boolean;
            storesLowerCaseQuotedIdentifiers(): boolean;
            storesMixedCaseIdentifiers(): boolean;
            storesMixedCaseQuotedIdentifiers(): boolean;
            storesUpperCaseIdentifiers(): boolean;
            storesUpperCaseQuotedIdentifiers(): boolean;
            supportsANSI92EntryLevelSQL(): boolean;
            supportsANSI92FullSQL(): boolean;
            supportsANSI92IntermediateSQL(): boolean;
            supportsAlterTableWithAddColumn(): boolean;
            supportsAlterTableWithDropColumn(): boolean;
            supportsBatchUpdates(): boolean;
            supportsCatalogsInDataManipulation(): boolean;
            supportsCatalogsInIndexDefinitions(): boolean;
            supportsCatalogsInPrivilegeDefinitions(): boolean;
            supportsCatalogsInProcedureCalls(): boolean;
            supportsCatalogsInTableDefinitions(): boolean;
            supportsColumnAliasing(): boolean;
            supportsConvert(): boolean;
            supportsConvert(fromType: Integer, toType: Integer): boolean;
            supportsCoreSQLGrammar(): boolean;
            supportsCorrelatedSubqueries(): boolean;
            supportsDataDefinitionAndDataManipulationTransactions(): boolean;
            supportsDataManipulationTransactionsOnly(): boolean;
            supportsDifferentTableCorrelationNames(): boolean;
            supportsExpressionsInOrderBy(): boolean;
            supportsExtendedSQLGrammar(): boolean;
            supportsFullOuterJoins(): boolean;
            supportsGetGeneratedKeys(): boolean;
            supportsGroupBy(): boolean;
            supportsGroupByBeyondSelect(): boolean;
            supportsGroupByUnrelated(): boolean;
            supportsIntegrityEnhancementFacility(): boolean;
            supportsLikeEscapeClause(): boolean;
            supportsLimitedOuterJoins(): boolean;
            supportsMinimumSQLGrammar(): boolean;
            supportsMixedCaseIdentifiers(): boolean;
            supportsMixedCaseQuotedIdentifiers(): boolean;
            supportsMultipleOpenResults(): boolean;
            supportsMultipleResultSets(): boolean;
            supportsMultipleTransactions(): boolean;
            supportsNamedParameters(): boolean;
            supportsNonNullableColumns(): boolean;
            supportsOpenCursorsAcrossCommit(): boolean;
            supportsOpenCursorsAcrossRollback(): boolean;
            supportsOpenStatementsAcrossCommit(): boolean;
            supportsOpenStatementsAcrossRollback(): boolean;
            supportsOrderByUnrelated(): boolean;
            supportsOuterJoins(): boolean;
            supportsPositionedDelete(): boolean;
            supportsPositionedUpdate(): boolean;
            supportsResultSetConcurrency(type: Integer, concurrency: Integer): boolean;
            supportsResultSetHoldability(holdability: Integer): boolean;
            supportsResultSetType(type: Integer): boolean;
            supportsSavepoints(): boolean;
            supportsSchemasInDataManipulation(): boolean;
            supportsSchemasInIndexDefinitions(): boolean;
            supportsSchemasInPrivilegeDefinitions(): boolean;
            supportsSchemasInProcedureCalls(): boolean;
            supportsSchemasInTableDefinitions(): boolean;
            supportsSelectForUpdate(): boolean;
            supportsStatementPooling(): boolean;
            supportsStoredFunctionsUsingCallSyntax(): boolean;
            supportsStoredProcedures(): boolean;
            supportsSubqueriesInComparisons(): boolean;
            supportsSubqueriesInExists(): boolean;
            supportsSubqueriesInIns(): boolean;
            supportsSubqueriesInQuantifieds(): boolean;
            supportsTableCorrelationNames(): boolean;
            supportsTransactionIsolationLevel(level: Integer): boolean;
            supportsTransactions(): boolean;
            supportsUnion(): boolean;
            supportsUnionAll(): boolean;
            updatesAreDetected(type: Integer): boolean;
            usesLocalFilePerTable(): boolean;
            usesLocalFiles(): boolean;
        }
        /**
         * A JDBC Date. For documentation of this class, see java.sql.Date
         * .
         */
        interface JdbcDate {
            after(when: JdbcDate): boolean;
            before(when: JdbcDate): boolean;
            getDate(): Integer;
            getMonth(): Integer;
            getTime(): Integer;
            getYear(): Integer;
            setDate(date: Integer): void;
            setMonth(month: Integer): void;
            setTime(milliseconds: Integer): void;
            setYear(year: Integer): void;
        }
        /**
         * A JDBC ParameterMetaData. For documentation of this class, see
         * java.sql.ParameterMetaData.
         */
        interface JdbcParameterMetaData {
            getParameterClassName(param: Integer): string;
            getParameterCount(): Integer;
            getParameterMode(param: Integer): Integer;
            getParameterType(param: Integer): Integer;
            getParameterTypeName(param: Integer): string;
            getPrecision(param: Integer): Integer;
            getScale(param: Integer): Integer;
            isNullable(param: Integer): Integer;
            isSigned(param: Integer): boolean;
        }
        /**
         * A JDBC PreparedStatement. For documentation of this class, see
         * java.sql.PreparedStatement.
         */
        interface JdbcPreparedStatement {
            addBatch(): void;
            addBatch(sql: string): void;
            cancel(): void;
            clearBatch(): void;
            clearParameters(): void;
            clearWarnings(): void;
            close(): void;
            execute(): boolean;
            execute(sql: string): boolean;
            execute(sql: string, autoGeneratedKeys: Integer, sql_: string, columnIndexes: Integer[]): boolean;
            execute(sql: string, autoGeneratedKeys: Integer, sql_: string, columnIndexes: Integer[]): boolean;
            execute(sql: string, columnNames: string[]): boolean;
            executeBatch(): Integer[];
            executeQuery(): JdbcResultSet;
            executeQuery(sql: string): JdbcResultSet;
            executeUpdate(): Integer;
            executeUpdate(sql: string): Integer;
            executeUpdate(sql: string, autoGeneratedKeys: Integer, sql_: string, columnIndexes: Integer[]): Integer;
            executeUpdate(sql: string, autoGeneratedKeys: Integer, sql_: string, columnIndexes: Integer[]): Integer;
            executeUpdate(sql: string, columnNames: string[]): Integer;
            getConnection(): JdbcConnection;
            getFetchDirection(): Integer;
            getFetchSize(): Integer;
            getGeneratedKeys(): JdbcResultSet;
            getMaxFieldSize(): Integer;
            getMaxRows(): Integer;
            getMetaData(): JdbcResultSetMetaData;
            getMoreResults(): boolean;
            getMoreResults(current: Integer): boolean;
            getParameterMetaData(): JdbcParameterMetaData;
            getQueryTimeout(): Integer;
            getResultSet(): JdbcResultSet;
            getResultSetConcurrency(): Integer;
            getResultSetHoldability(): Integer;
            getResultSetType(): Integer;
            getUpdateCount(): Integer;
            getWarnings(): string[];
            isClosed(): boolean;
            isPoolable(): boolean;
            setArray(parameterIndex: Integer, x: JdbcArray): void;
            setBigDecimal(parameterIndex: Integer, x: BigNumber): void;
            setBlob(parameterIndex: Integer, x: JdbcBlob): void;
            setBoolean(parameterIndex: Integer, x: boolean): void;
            setByte(parameterIndex: Integer, x: Byte): void;
            setBytes(parameterIndex: Integer, x: Byte[]): void;
            setClob(parameterIndex: Integer, x: JdbcClob): void;
            setCursorName(name: string): void;
            setDate(parameterIndex: Integer, x: JdbcDate): void;
            setDate(parameterIndex: Integer, x: JdbcDate, timeZone: string): void;
            setDouble(parameterIndex: Integer, x: number): void;
            setEscapeProcessing(enable: boolean): void;
            setFetchDirection(direction: Integer): void;
            setFetchSize(rows: Integer): void;
            setFloat(parameterIndex: Integer, x: number): void;
            setInt(parameterIndex: Integer, x: Integer): void;
            setLong(parameterIndex: Integer, x: Integer): void;
            setMaxFieldSize(max: Integer): void;
            setMaxRows(max: Integer): void;
            setNClob(parameterIndex: Integer, x: JdbcClob): void;
            setNString(parameterIndex: Integer, x: string): void;
            setNull(parameterIndex: Integer, sqlType: Integer): void;
            setNull(parameterIndex: Integer, sqlType: Integer, typeName: string): void;
            setObject(index: Integer, x: any): void;
            setObject(parameterIndex: Integer, x: any, targetSqlType: Integer): void;
            setObject(parameterIndex: Integer, x: any, targetSqlType: Integer, scaleOrLength: Integer): void;
            setPoolable(poolable: boolean): void;
            setQueryTimeout(seconds: Integer): void;
            setRef(parameterIndex: Integer, x: JdbcRef): void;
            setRowId(parameterIndex: Integer, x: JdbcRowId): void;
            setSQLXML(parameterIndex: Integer, x: JdbcSQLXML): void;
            setShort(parameterIndex: Integer, x: Integer): void;
            setString(parameterIndex: Integer, x: string): void;
            setTime(parameterIndex: Integer, x: JdbcTime): void;
            setTime(parameterIndex: Integer, x: JdbcTime, timeZone: string): void;
            setTimestamp(parameterIndex: Integer, x: JdbcTimestamp): void;
            setTimestamp(parameterIndex: Integer, x: JdbcTimestamp, timeZone: string): void;
            setURL(parameterIndex: Integer, x: string): void;
        }
        /**
         * A JDBC Ref. For documentation of this class, see java.sql.Ref.
         */
        interface JdbcRef {
            getBaseTypeName(): string;
            getObject(): any;
            setObject(object: any): void;
        }
        /**
         * A JDBC ResultSet. For documentation of this class, see java.sql.ResultSet
         * .
         */
        interface JdbcResultSet {
            absolute(row: Integer): boolean;
            afterLast(): void;
            beforeFirst(): void;
            cancelRowUpdates(): void;
            clearWarnings(): void;
            close(): void;
            deleteRow(): void;
            findColumn(columnLabel: string): Integer;
            first(): boolean;
            getArray(columnIndex: Integer): JdbcArray;
            getArray(columnLabel: string): JdbcArray;
            getBigDecimal(columnIndex: Integer): BigNumber;
            getBigDecimal(columnLabel: string): BigNumber;
            getBlob(columnIndex: Integer): JdbcBlob;
            getBlob(columnLabel: string): JdbcBlob;
            getBoolean(columnIndex: Integer): boolean;
            getBoolean(columnLabel: string): boolean;
            getByte(columnIndex: Integer): Byte;
            getByte(columnLabel: string): Byte;
            getBytes(columnIndex: Integer): Byte[];
            getBytes(columnLabel: string): Byte[];
            getClob(columnIndex: Integer): JdbcClob;
            getClob(columnLabel: string): JdbcClob;
            getConcurrency(): Integer;
            getCursorName(): string;
            getDate(columnIndex: Integer): JdbcDate;
            getDate(columnIndex: Integer, timeZone: string): JdbcDate;
            getDate(columnLabel: string): JdbcDate;
            getDate(columnLabel: string, timeZone: string): JdbcDate;
            getDouble(columnIndex: Integer): number;
            getDouble(columnLabel: string): number;
            getFetchDirection(): Integer;
            getFetchSize(): Integer;
            getFloat(columnIndex: Integer): number;
            getFloat(columnLabel: string): number;
            getHoldability(): Integer;
            getInt(columnIndex: Integer): Integer;
            getInt(columnLabel: string): Integer;
            getLong(columnIndex: Integer): Integer;
            getLong(columnLabel: string): Integer;
            getMetaData(): JdbcResultSetMetaData;
            getNClob(columnIndex: Integer): JdbcClob;
            getNClob(columnLabel: string): JdbcClob;
            getNString(columnIndex: Integer): string;
            getNString(columnLabel: string): string;
            getObject(columnIndex: Integer): any;
            getObject(columnLabel: string): any;
            getRef(columnIndex: Integer): JdbcRef;
            getRef(columnLabel: string): JdbcRef;
            getRow(): Integer;
            getRowId(columnIndex: Integer): JdbcRowId;
            getRowId(columnLabel: string): JdbcRowId;
            getSQLXML(columnIndex: Integer): JdbcSQLXML;
            getSQLXML(columnLabel: string): JdbcSQLXML;
            getShort(columnIndex: Integer): Integer;
            getShort(columnLabel: string): Integer;
            getStatement(): JdbcStatement;
            getString(columnIndex: Integer): string;
            getString(columnLabel: string): string;
            getTime(columnIndex: Integer): JdbcTime;
            getTime(columnIndex: Integer, timeZone: string): JdbcTime;
            getTime(columnLabel: string): JdbcTime;
            getTime(columnLabel: string, timeZone: string): JdbcTime;
            getTimestamp(columnIndex: Integer): JdbcTimestamp;
            getTimestamp(columnIndex: Integer, timeZone: string): JdbcTimestamp;
            getTimestamp(columnLabel: string): JdbcTimestamp;
            getTimestamp(columnLabel: string, timeZone: string): JdbcTimestamp;
            getType(): Integer;
            getURL(columnIndex: Integer): string;
            getURL(columnLabel: string): string;
            getWarnings(): string[];
            insertRow(): void;
            isAfterLast(): boolean;
            isBeforeFirst(): boolean;
            isClosed(): boolean;
            isFirst(): boolean;
            isLast(): boolean;
            last(): boolean;
            moveToCurrentRow(): void;
            moveToInsertRow(): void;
            next(): boolean;
            previous(): boolean;
            refreshRow(): void;
            relative(rows: Integer): boolean;
            rowDeleted(): boolean;
            rowInserted(): boolean;
            rowUpdated(): boolean;
            setFetchDirection(direction: Integer): void;
            setFetchSize(rows: Integer): void;
            updateArray(columnIndex: Integer, x: JdbcArray): void;
            updateArray(columnLabel: string, x: JdbcArray): void;
            updateBigDecimal(columnIndex: Integer, x: BigNumber): void;
            updateBigDecimal(columnLabel: string, x: BigNumber): void;
            updateBlob(columnIndex: Integer, x: JdbcBlob): void;
            updateBlob(columnLabel: string, x: JdbcBlob): void;
            updateBoolean(columnIndex: Integer, x: boolean): void;
            updateBoolean(columnLabel: string, x: boolean): void;
            updateByte(columnIndex: Integer, x: Byte): void;
            updateByte(columnLabel: string, x: Byte): void;
            updateBytes(columnIndex: Integer, x: Byte[]): void;
            updateBytes(columnLabel: string, x: Byte[]): void;
            updateClob(columnIndex: Integer, x: JdbcClob): void;
            updateClob(columnLabel: string, x: JdbcClob): void;
            updateDate(columnIndex: Integer, x: JdbcDate): void;
            updateDate(columnLabel: string, x: JdbcDate): void;
            updateDouble(columnIndex: Integer, x: number): void;
            updateDouble(columnLabel: string, x: number): void;
            updateFloat(columnIndex: Integer, x: number): void;
            updateFloat(columnLabel: string, x: number): void;
            updateInt(columnIndex: Integer, x: Integer): void;
            updateInt(columnLabel: string, x: Integer): void;
            updateLong(columnIndex: Integer, x: Integer): void;
            updateLong(columnLabel: string, x: Integer): void;
            updateNClob(columnIndex: Integer, x: JdbcClob): void;
            updateNClob(columnLabel: string, x: JdbcClob): void;
            updateNString(columnIndex: Integer, x: string): void;
            updateNString(columnLabel: string, x: string): void;
            updateNull(columnIndex: Integer): void;
            updateNull(columnLabel: string): void;
            updateObject(columnIndex: Integer, x: any): void;
            updateObject(columnIndex: Integer, x: any, scaleOrLength: Integer): void;
            updateObject(columnLabel: string, x: any): void;
            updateObject(columnLabel: string, x: any, scaleOrLength: Integer): void;
            updateRef(columnIndex: Integer, x: JdbcRef): void;
            updateRef(columnLabel: string, x: JdbcRef): void;
            updateRow(): void;
            updateRowId(columnIndex: Integer, x: JdbcRowId): void;
            updateRowId(columnLabel: string, x: JdbcRowId): void;
            updateSQLXML(columnIndex: Integer, x: JdbcSQLXML): void;
            updateSQLXML(columnLabel: string, x: JdbcSQLXML): void;
            updateShort(columnIndex: Integer, x: Integer): void;
            updateShort(columnLabel: string, x: Integer): void;
            updateString(columnIndex: Integer, x: string): void;
            updateString(columnLabel: string, x: string): void;
            updateTime(columnIndex: Integer, x: JdbcTime): void;
            updateTime(columnLabel: string, x: JdbcTime): void;
            updateTimestamp(columnIndex: Integer, x: JdbcTimestamp): void;
            updateTimestamp(columnLabel: string, x: JdbcTimestamp): void;
            wasNull(): boolean;
        }
        /**
         * A JDBC ResultSetMetaData. For documentation of this class, see
         * java.sql.ResultSetMetaData.
         */
        interface JdbcResultSetMetaData {
            getCatalogName(column: Integer): string;
            getColumnClassName(column: Integer): string;
            getColumnCount(): Integer;
            getColumnDisplaySize(column: Integer): Integer;
            getColumnLabel(column: Integer): string;
            getColumnName(column: Integer): string;
            getColumnType(column: Integer): Integer;
            getColumnTypeName(column: Integer): string;
            getPrecision(column: Integer): Integer;
            getScale(column: Integer): Integer;
            getSchemaName(column: Integer): string;
            getTableName(column: Integer): string;
            isAutoIncrement(column: Integer): boolean;
            isCaseSensitive(column: Integer): boolean;
            isCurrency(column: Integer): boolean;
            isDefinitelyWritable(column: Integer): boolean;
            isNullable(column: Integer): Integer;
            isReadOnly(column: Integer): boolean;
            isSearchable(column: Integer): boolean;
            isSigned(column: Integer): boolean;
            isWritable(column: Integer): boolean;
        }
        /**
         * A JDBC RowId. For documentation of this class, see java.sql.RowId
         * .
         */
        interface JdbcRowId {
            getBytes(): Byte[];
        }
        /**
         * A JDBC SQLXML. For documentation of this class, see java.sql.SQLXML
         * .
         */
        interface JdbcSQLXML {
            free(): void;
            getString(): string;
            setString(value: string): void;
        }
        /**
         * A JDBC Savepoint. For documentation of this class, see java.sql.Savepoint
         * .
         */
        interface JdbcSavepoint {
            getSavepointId(): Integer;
            getSavepointName(): string;
        }
        /**
         * A JDBC Statement. For documentation of this class, see java.sql.Statement
         * .
         */
        interface JdbcStatement {
            addBatch(sql: string): void;
            cancel(): void;
            clearBatch(): void;
            clearWarnings(): void;
            close(): void;
            execute(sql: string): boolean;
            execute(sql: string, autoGeneratedKeys: Integer, sql_: string, columnIndexes: Integer[]): boolean;
            execute(sql: string, autoGeneratedKeys: Integer, sql_: string, columnIndexes: Integer[]): boolean;
            execute(sql: string, columnNames: string[]): boolean;
            executeBatch(): Integer[];
            executeQuery(sql: string): JdbcResultSet;
            executeUpdate(sql: string): Integer;
            executeUpdate(sql: string, autoGeneratedKeys: Integer, sql_: string, columnIndexes: Integer[]): Integer;
            executeUpdate(sql: string, autoGeneratedKeys: Integer, sql_: string, columnIndexes: Integer[]): Integer;
            executeUpdate(sql: string, columnNames: string[]): Integer;
            getConnection(): JdbcConnection;
            getFetchDirection(): Integer;
            getFetchSize(): Integer;
            getGeneratedKeys(): JdbcResultSet;
            getMaxFieldSize(): Integer;
            getMaxRows(): Integer;
            getMoreResults(): boolean;
            getMoreResults(current: Integer): boolean;
            getQueryTimeout(): Integer;
            getResultSet(): JdbcResultSet;
            getResultSetConcurrency(): Integer;
            getResultSetHoldability(): Integer;
            getResultSetType(): Integer;
            getUpdateCount(): Integer;
            getWarnings(): string[];
            isClosed(): boolean;
            isPoolable(): boolean;
            setCursorName(name: string): void;
            setEscapeProcessing(enable: boolean): void;
            setFetchDirection(direction: Integer): void;
            setFetchSize(rows: Integer): void;
            setMaxFieldSize(max: Integer): void;
            setMaxRows(max: Integer): void;
            setPoolable(poolable: boolean): void;
            setQueryTimeout(seconds: Integer): void;
        }
        /**
         * A JDBC Struct. For documentation of this class, see java.sql.Struct
         * .
         */
        interface JdbcStruct {
            getAttributes(): any[];
            getSQLTypeName(): string;
        }
        /**
         * A JDBC Time. For documentation of this class, see java.sql.Time
         * .
         */
        interface JdbcTime {
            after(when: JdbcTime): boolean;
            before(when: JdbcTime): boolean;
            getHours(): Integer;
            getMinutes(): Integer;
            getSeconds(): Integer;
            getTime(): Integer;
            setHours(hours: Integer): void;
            setMinutes(minutes: Integer): void;
            setSeconds(seconds: Integer): void;
            setTime(milliseconds: Integer): void;
        }
        /**
         * A JDBC Timestamp. For documentation of this class, see java.sql.Timestamp
         * .
         */
        interface JdbcTimestamp {
            after(when: JdbcTimestamp): boolean;
            before(when: JdbcTimestamp): boolean;
            getDate(): Integer;
            getHours(): Integer;
            getMinutes(): Integer;
            getMonth(): Integer;
            getNanos(): Integer;
            getSeconds(): Integer;
            getTime(): Integer;
            getYear(): Integer;
            setDate(date: Integer): void;
            setHours(hours: Integer): void;
            setMinutes(minutes: Integer): void;
            setMonth(month: Integer): void;
            setNanos(nanoseconds: Integer): void;
            setSeconds(seconds: Integer): void;
            setTime(milliseconds: Integer): void;
            setYear(year: Integer): void;
        }
    }
}

declare var Jdbc: GoogleAppsScript.JDBC.Jdbc;
