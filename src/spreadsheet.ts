/**
 * @fileoverview Google Apps Script Spreadsheet service types
 */

import type { Integer } from "./types.ts";
import type { Date, Blob, User, Ui, ColorType } from "./base.ts";

/**
 * An enumeration of the types of series used to calculate auto-filled values.
 */
export enum AutoFillSeries {
    DEFAULT_SERIES,
    ALTERNATE_SERIES,
}

/**
 * An enumeration representing the possible directions for movement within a spreadsheet.
 */
export enum Direction {
    UP,
    DOWN,
    PREVIOUS,
    NEXT,
}

/**
 * An enumeration representing the sort order.
 */
export enum SortOrder {
    ASCENDING,
    DESCENDING,
}

/**
 * An enumeration of the types of boolean criteria.
 */
export enum BooleanCriteria {
    CELL_EMPTY,
    CELL_NOT_EMPTY,
    DATE_AFTER,
    DATE_BEFORE,
    DATE_EQUAL_TO,
    DATE_NOT_EQUAL_TO,
    DATE_BETWEEN,
    DATE_NOT_BETWEEN,
    DATE_IS_VALID,
    NUMBER_BETWEEN,
    NUMBER_EQUAL_TO,
    NUMBER_GREATER_THAN,
    NUMBER_GREATER_THAN_OR_EQUAL_TO,
    NUMBER_LESS_THAN,
    NUMBER_LESS_THAN_OR_EQUAL_TO,
    NUMBER_NOT_BETWEEN,
    NUMBER_NOT_EQUAL_TO,
    TEXT_CONTAINS,
    TEXT_DOES_NOT_CONTAIN,
    TEXT_EQUAL_TO,
    TEXT_IS_EMAIL,
    TEXT_IS_URL,
    TEXT_NOT_EQUAL_TO,
    VALUE_IN_LIST,
    VALUE_IN_RANGE,
    CUSTOM_FORMULA,
}

/**
 * Access and modify spreadsheet ranges.
 */
export interface Range {
    activate(): Range;
    activateAsCurrentCell(): Range;
    addDeveloperMetadata(key: string): Range;
    addDeveloperMetadata(key: string, visibility: DeveloperMetadataVisibility): Range;
    addDeveloperMetadata(key: string, value: string): Range;
    addDeveloperMetadata(key: string, value: string, visibility: DeveloperMetadataVisibility): Range;
    breakApart(): Range;
    canEdit(): boolean;
    check(): Range;
    clear(): Range;
    clear(options: {
        commentsOnly?: boolean;
        contentsOnly?: boolean;
        formatOnly?: boolean;
        validationsOnly?: boolean;
        skipFilteredRows?: boolean;
    }): Range;
    clearContent(): Range;
    clearDataValidations(): Range;
    clearFormat(): Range;
    clearNote(): Range;
    copyFormatToRange(gridId: Integer, column: Integer, columnEnd: Integer, row: Integer, rowEnd: Integer): void;
    copyFormatToRange(sheet: Sheet, column: Integer, columnEnd: Integer, row: Integer, rowEnd: Integer): void;
    copyTo(destination: Range): void;
    copyTo(destination: Range, copyPasteType: CopyPasteType, transposed: boolean): void;
    copyTo(destination: Range, options: { copyPasteType?: CopyPasteType; transposed?: boolean }): void;
    copyValuesToRange(gridId: Integer, column: Integer, columnEnd: Integer, row: Integer, rowEnd: Integer): void;
    copyValuesToRange(sheet: Sheet, column: Integer, columnEnd: Integer, row: Integer, rowEnd: Integer): void;
    createDataSourceTable(dataSource: DataSource): DataSourceTable;
    createFilter(): Filter;
    createPivotTable(sourceData: Range): PivotTable;
    deleteCells(shiftDimension: Dimension): Range;
    getA1Notation(): string;
    getBackground(): string;
    getBackgrounds(): string[][];
    getCell(row: Integer, column: Integer): Range;
    getColumn(): Integer;
    getDataRegion(): Range;
    getDataRegion(dimension: Dimension): Range;
    getDataSourceUrl(): string | null;
    getDataTable(): DataTable;
    getDataTable(firstRowIsHeader: boolean): DataTable;
    getDataValidation(): DataValidation | null;
    getDataValidations(): Array<DataValidation | null>[];
    getDisplayValue(): string;
    getDisplayValues(): string[][];
    getFilter(): Filter | null;
    getFontColorObject(): Color;
    getFontColorObjects(): Color[][];
    getFontFamily(): string;
    getFontFamilies(): string[][];
    getFontLine(): string;
    getFontLines(): string[][];
    getFontSize(): Integer;
    getFontSizes(): Integer[][];
    getFontStyle(): string;
    getFontStyles(): string[][];
    getFontWeight(): string;
    getFontWeights(): string[][];
    getFormula(): string;
    getFormulaR1C1(): string;
    getFormulas(): string[][];
    getFormulasR1C1(): string[][];
    getGridId(): Integer;
    getHeight(): Integer;
    getHorizontalAlignment(): string;
    getHorizontalAlignments(): string[][];
    getLastColumn(): Integer;
    getLastRow(): Integer;
    getMergedRanges(): Range[];
    getNote(): string;
    getNotes(): string[][];
    getNumColumns(): Integer;
    getNumRows(): Integer;
    getNumberFormat(): string;
    getNumberFormats(): string[][];
    getRichTextValue(): RichTextValue | null;
    getRichTextValues(): Array<RichTextValue | null>[];
    getRow(): Integer;
    getRowIndex(): Integer;
    getSheet(): Sheet;
    getTextDirection(): TextDirection | null;
    getTextDirections(): Array<TextDirection | null>[];
    getTextRotation(): TextRotation;
    getTextRotations(): TextRotation[];
    getTextStyle(): TextStyle;
    getTextStyles(): TextStyle[][];
    getValue(): unknown;
    getValues(): unknown[][];
    getVerticalAlignment(): string;
    getVerticalAlignments(): string[][];
    getWidth(): Integer;
    getWrap(): boolean;
    getWrapStrategies(): WrapStrategy[][];
    getWrapStrategy(): WrapStrategy;
    getWraps(): boolean[][];
    insertCells(shiftDimension: Dimension): Range;
    insertCheckboxes(): Range;
    insertCheckboxes(checkedValue: unknown): Range;
    insertCheckboxes(checkedValue: unknown, uncheckedValue: unknown): Range;
    isBlank(): boolean;
    isChecked(): boolean | null;
    isEndColumnBounded(): boolean;
    isEndRowBounded(): boolean;
    isPartOfMerge(): boolean;
    isStartColumnBounded(): boolean;
    isStartRowBounded(): boolean;
    merge(): Range;
    mergeAcross(): Range;
    mergeVertically(): Range;
    moveTo(target: Range): void;
    offset(rowOffset: Integer, columnOffset: Integer): Range;
    offset(rowOffset: Integer, columnOffset: Integer, numRows: Integer): Range;
    offset(rowOffset: Integer, columnOffset: Integer, numRows: Integer, numColumns: Integer): Range;
    protect(): Protection;
    randomize(): Range;
    removeCheckboxes(): Range;
    removeDuplicates(): Range;
    removeDuplicates(columnsToCompare: Integer[]): Range;
    setBackground(color: string): Range;
    setBackgroundObject(color: Color): Range;
    setBackgroundObjects(color: Color[][]): Range;
    setBackgroundRGB(red: Integer, green: Integer, blue: Integer): Range;
    setBackgrounds(color: string[][]): Range;
    setBorder(top: boolean, left: boolean, bottom: boolean, right: boolean, vertical: boolean, horizontal: boolean): Range;
    setBorder(
        top: boolean,
        left: boolean,
        bottom: boolean,
        right: boolean,
        vertical: boolean,
        horizontal: boolean,
        color: string,
        style: BorderStyle,
    ): Range;
    setDataValidation(rule: DataValidation | null): Range;
    setDataValidations(rules: Array<DataValidation | null>[]): Range;
    setFontColor(color: string): Range;
    setFontColorObject(color: Color): Range;
    setFontColorObjects(colors: Color[][]): Range;
    setFontColors(colors: string[][]): Range;
    setFontFamily(fontFamily: string): Range;
    setFontFamilies(fontFamilies: string[][]): Range;
    setFontLine(fontLine: string): Range;
    setFontLines(fontLines: string[][]): Range;
    setFontSize(size: Integer): Range;
    setFontSizes(sizes: Integer[][]): Range;
    setFontStyle(fontStyle: string): Range;
    setFontStyles(fontStyles: string[][]): Range;
    setFontWeight(fontWeight: string): Range;
    setFontWeights(fontWeights: string[][]): Range;
    setFormula(formula: string): Range;
    setFormulaR1C1(formula: string): Range;
    setFormulas(formulas: string[][]): Range;
    setFormulasR1C1(formulas: string[][]): Range;
    setHorizontalAlignment(alignment: string): Range;
    setHorizontalAlignments(alignments: string[][]): Range;
    setNote(note: string): Range;
    setNotes(notes: string[][]): Range;
    setNumberFormat(numberFormat: string): Range;
    setNumberFormats(numberFormats: string[][]): Range;
    setRichTextValue(value: RichTextValue): Range;
    setRichTextValues(values: RichTextValue[][]): Range;
    setShowHyperlink(showHyperlink: boolean): Range;
    setTextDirection(direction: TextDirection | null): Range;
    setTextDirections(directions: Array<TextDirection | null>[]): Range;
    setTextRotation(degrees: Integer): Range;
    setTextRotation(rotation: TextRotation): Range;
    setTextRotations(rotations: TextRotation[]): Range;
    setTextStyle(style: TextStyle): Range;
    setTextStyles(styles: TextStyle[][]): Range;
    setValue(value: unknown): Range;
    setValues(values: unknown[][]): Range;
    setVerticalAlignment(alignment: string): Range;
    setVerticalAlignments(alignments: string[][]): Range;
    setWrap(isWrapEnabled: boolean): Range;
    setWrapStrategy(strategy: WrapStrategy): Range;
    setWrapStrategies(strategies: WrapStrategy[][]): Range;
    setWraps(isWrapEnabled: boolean[][]): Range;
    shiftColumnGroupDepth(delta: Integer): Range;
    shiftRowGroupDepth(delta: Integer): Range;
    sort(sortSpecObj: unknown): Range;
    splitTextToColumns(): void;
    splitTextToColumns(delimiter: string): void;
    splitTextToColumns(delimiter: string, splitByEach: boolean): void;
    trimWhitespace(): Range;
    uncheck(): Range;
}

// Placeholder interfaces - these would need full implementation
export interface Sheet {
    getName(): string;
    getRange(a1Notation: string): Range;
    getRange(row: Integer, column: Integer): Range;
    getRange(row: Integer, column: Integer, numRows: Integer): Range;
    getRange(row: Integer, column: Integer, numRows: Integer, numColumns: Integer): Range;
    // ... many more methods
}

export interface Spreadsheet {
    getActiveSheet(): Sheet;
    getSheetByName(name: string): Sheet | null;
    getSheets(): Sheet[];
    insertSheet(): Sheet;
    insertSheet(sheetName: string): Sheet;
    // ... many more methods
}

export interface SpreadsheetApp {
    AutoFillSeries: typeof AutoFillSeries;
    BooleanCriteria: typeof BooleanCriteria;
    Direction: typeof Direction;
    SortOrder: typeof SortOrder;
    ColorType: typeof ColorType;
    create(name: string): Spreadsheet;
    getActive(): Spreadsheet;
    getActiveRange(): Range;
    getActiveSheet(): Sheet;
    getActiveSpreadsheet(): Spreadsheet;
    getUi(): Ui;
    openById(id: string): Spreadsheet;
    openByUrl(url: string): Spreadsheet;
    // ... many more methods
}

// Placeholder types that would need full implementation
export interface DataValidation { }
export interface DeveloperMetadataVisibility { }
export interface CopyPasteType { }
export interface Dimension { }
export interface DataSource { }
export interface DataSourceTable { }
export interface Filter { }
export interface PivotTable { }
export interface DataTable { }
export interface Color { }
export interface RichTextValue { }
export interface TextDirection { }
export interface TextRotation { }
export interface TextStyle { }
export interface WrapStrategy { }
export interface Protection { }
export interface BorderStyle { }

// Global SpreadsheetApp instance
export declare const SpreadsheetApp: SpreadsheetApp;
